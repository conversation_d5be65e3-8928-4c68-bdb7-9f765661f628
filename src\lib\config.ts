/* eslint-disable @typescript-eslint/no-explicit-any, no-console, @typescript-eslint/no-non-null-assertion */

import { getStorage } from '@/lib/db';

import { AdminConfig } from './admin.types';
import runtimeConfig from './runtime';

export interface ApiSite {
  key: string;
  api: string;
  name: string;
  detail?: string;
}

interface ConfigFileStruct {
  cache_time?: number;
  api_site: {
    [key: string]: ApiSite;
  };
}

export const API_CONFIG = {
  search: {
    path: '?ac=videolist&wd=',
    pagePath: '?ac=videolist&wd={query}&pg={page}',
    headers: {
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      Accept: 'application/json',
    },
  },
  detail: {
    path: '?ac=videolist&ids=',
    headers: {
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      Accept: 'application/json',
    },
  },
};

// 在模块加载时根据环境决定配置来源
let fileConfig: ConfigFileStruct;
let cachedConfig: AdminConfig;

async function initConfig() {
  if (process.env.DOCKER_ENV === 'true') {
    // 这里用 eval("require") 避开静态分析，防止 Edge Runtime 打包时报 "Can't resolve 'fs'"
    // 在实际 Node.js 运行时才会执行到，因此不会影响 Edge 环境。
    // eslint-disable-next-line @typescript-eslint/no-implied-eval
    const _require = eval('require') as NodeRequire;
    const fs = _require('fs') as typeof import('fs');
    const path = _require('path') as typeof import('path');

    const configPath = path.join(process.cwd(), 'config.json');
    const raw = fs.readFileSync(configPath, 'utf-8');
    fileConfig = JSON.parse(raw) as ConfigFileStruct;
    console.log('load dynamic config success');
  } else {
    // 默认使用编译时生成的配置
    fileConfig = runtimeConfig as unknown as ConfigFileStruct;
  }
  const storageType = process.env.NEXT_PUBLIC_STORAGE_TYPE || 'localstorage';
  if (storageType !== 'localstorage') {
    // 数据库存储，读取并补全管理员配置
    const storage = getStorage();
    (async () => {
      try {
        // 尝试从数据库获取管理员配置
        let adminConfig: AdminConfig | null = null;
        if (storage && typeof (storage as any).getAdminConfig === 'function') {
          adminConfig = await (storage as any).getAdminConfig();
        }

        // 获取所有用户名，用于补全 Users
        let userNames: string[] = [];
        if (storage && typeof (storage as any).getAllUsers === 'function') {
          try {
            userNames = await (storage as any).getAllUsers();
          } catch (e) {
            console.error('获取用户列表失败:', e);
          }
        }

        // 从文件中获取源信息，用于补全源
        const apiSiteEntries = Object.entries(fileConfig.api_site);

        if (adminConfig) {
          // 补全 SourceConfig
          const existed = new Set(
            (adminConfig.SourceConfig || []).map((s) => s.key)
          );
          apiSiteEntries.forEach(([key, site]) => {
            if (!existed.has(key)) {
              adminConfig!.SourceConfig.push({
                key,
                name: site.name,
                api: site.api,
                detail: site.detail,
                from: 'config',
                disabled: false,
              });
            }
          });

          // 检查现有源是否在 fileConfig.api_site 中，如果不在则标记为 custom
          const apiSiteKeys = new Set(apiSiteEntries.map(([key]) => key));
          adminConfig.SourceConfig.forEach((source) => {
            if (!apiSiteKeys.has(source.key)) {
              source.from = 'custom';
            }
          });

          const existedUsers = new Set(
            (adminConfig.UserConfig.Users || []).map((u) => u.username)
          );
          userNames.forEach((uname) => {
            if (!existedUsers.has(uname)) {
              adminConfig!.UserConfig.Users.push({
                username: uname,
                role: 'user',
              });
            }
          });
          // 站长
          const ownerUser = process.env.USERNAME;
          if (ownerUser) {
            adminConfig!.UserConfig.Users =
              adminConfig!.UserConfig.Users.filter(
                (u) => u.username !== ownerUser
              );
            adminConfig!.UserConfig.Users.unshift({
              username: ownerUser,
              role: 'owner',
            });
          }
        } else {
          // 数据库中没有配置，创建新的管理员配置
          let allUsers = userNames.map((uname) => ({
            username: uname,
            role: 'user',
          }));
          const ownerUser = process.env.USERNAME;
          if (ownerUser) {
            allUsers = allUsers.filter((u) => u.username !== ownerUser);
            allUsers.unshift({
              username: ownerUser,
              role: 'owner',
            });
          }
          adminConfig = {
            SiteConfig: {
              SiteName: process.env.SITE_NAME || 'MoonTV',
              Announcement:
                process.env.NEXT_PUBLIC_ANNOUNCEMENT ||
                '本网站仅提供影视信息搜索服务，所有内容均来自第三方网站。本站不存储任何视频资源，不对任何内容的准确性、合法性、完整性负责。',
              SearchDownstreamMaxPage:
                Number(process.env.NEXT_PUBLIC_SEARCH_MAX_PAGE) || 5,
              SiteInterfaceCacheTime: fileConfig.cache_time || 7200,
              SearchResultDefaultAggregate:
                process.env.NEXT_PUBLIC_AGGREGATE_SEARCH_RESULT !== 'false',
            },
            UserConfig: {
              AllowRegister: process.env.NEXT_PUBLIC_ENABLE_REGISTER === 'true',
              Users: allUsers as any,
            },
            SourceConfig: apiSiteEntries.map(([key, site]) => ({
              key,
              name: site.name,
              api: site.api,
              detail: site.detail,
              from: 'config',
              disabled: false,
            })),
          };
        }

        // 写回数据库（更新/创建）
        if (storage && typeof (storage as any).setAdminConfig === 'function') {
          await (storage as any).setAdminConfig(adminConfig);
        }

        // 更新缓存
        cachedConfig = adminConfig;
      } catch (err) {
        console.error('加载管理员配置失败:', err);
      }
    })();
  } else {
    // 本地存储直接使用文件配置
    cachedConfig = {
      SiteConfig: {
        SiteName: process.env.SITE_NAME || 'MoonTV',
        Announcement:
          process.env.NEXT_PUBLIC_ANNOUNCEMENT ||
          '本网站仅提供影视信息搜索服务，所有内容均来自第三方网站。本站不存储任何视频资源，不对任何内容的准确性、合法性、完整性负责。',
        SearchDownstreamMaxPage:
          Number(process.env.NEXT_PUBLIC_SEARCH_MAX_PAGE) || 5,
        SiteInterfaceCacheTime: fileConfig.cache_time || 7200,
        SearchResultDefaultAggregate:
          process.env.NEXT_PUBLIC_AGGREGATE_SEARCH_RESULT !== 'false',
      },
      UserConfig: {
        AllowRegister: process.env.NEXT_PUBLIC_ENABLE_REGISTER === 'true',
        Users: [],
      },
      SourceConfig: Object.entries(fileConfig.api_site).map(([key, site]) => ({
        key,
        name: site.name,
        api: site.api,
        detail: site.detail,
        from: 'config',
        disabled: false,
      })),
    } as AdminConfig;
  }
}

initConfig();

export function getConfig(): AdminConfig {
  return cachedConfig;
}

export async function resetConfig() {
  const storage = getStorage();
  // 获取所有用户名，用于补全 Users
  let userNames: string[] = [];
  if (storage && typeof (storage as any).getAllUsers === 'function') {
    try {
      userNames = await (storage as any).getAllUsers();
    } catch (e) {
      console.error('获取用户列表失败:', e);
    }
  }

  // 从文件中获取源信息，用于补全源
  const apiSiteEntries = Object.entries(fileConfig.api_site);
  let allUsers = userNames.map((uname) => ({
    username: uname,
    role: 'user',
  }));
  const ownerUser = process.env.USERNAME;
  if (ownerUser) {
    allUsers = allUsers.filter((u) => u.username !== ownerUser);
    allUsers.unshift({
      username: ownerUser,
      role: 'owner',
    });
  }
  const adminConfig = {
    SiteConfig: {
      SiteName: process.env.SITE_NAME || 'MoonTV',
      Announcement:
        process.env.NEXT_PUBLIC_ANNOUNCEMENT ||
        '本网站仅提供影视信息搜索服务，所有内容均来自第三方网站。本站不存储任何视频资源，不对任何内容的准确性、合法性、完整性负责。',
      SearchDownstreamMaxPage:
        Number(process.env.NEXT_PUBLIC_SEARCH_MAX_PAGE) || 5,
      SiteInterfaceCacheTime: fileConfig.cache_time || 7200,
      SearchResultDefaultAggregate:
        process.env.NEXT_PUBLIC_AGGREGATE_SEARCH_RESULT !== 'false',
    },
    UserConfig: {
      AllowRegister: process.env.NEXT_PUBLIC_ENABLE_REGISTER === 'true',
      Users: allUsers as any,
    },
    SourceConfig: apiSiteEntries.map(([key, site]) => ({
      key,
      name: site.name,
      api: site.api,
      detail: site.detail,
      from: 'config',
      disabled: false,
    })),
  } as AdminConfig;

  if (storage && typeof (storage as any).setAdminConfig === 'function') {
    await (storage as any).setAdminConfig(adminConfig);
  }

  cachedConfig.SiteConfig = adminConfig.SiteConfig;
  cachedConfig.UserConfig = adminConfig.UserConfig;
  cachedConfig.SourceConfig = adminConfig.SourceConfig;
}

export function getCacheTime(): number {
  const config = getConfig();
  return config.SiteConfig.SiteInterfaceCacheTime || 7200;
}

export function getAvailableApiSites(): ApiSite[] {
  const config = getConfig();
  return config.SourceConfig.filter((s) => !s.disabled).map((s) => ({
    key: s.key,
    name: s.name,
    api: s.api,
    detail: s.detail,
  }));
}
